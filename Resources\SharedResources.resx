<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Navigation -->
  <data name="NavHome" xml:space="preserve">
    <value>Ana Sayfa</value>
  </data>
  <data name="NavAbout" xml:space="preserve">
    <value>Hakkında</value>
  </data>
  <data name="NavQuran" xml:space="preserve">
    <value>Kur'an</value>
  </data>
  <data name="NavLessons" xml:space="preserve">
    <value>Dersler</value>
  </data>
  <data name="NavVideos" xml:space="preserve">
    <value>Videolar</value>
  </data>
  <data name="NavArticles" xml:space="preserve">
    <value>Makaleler</value>
  </data>
  <data name="NavBooks" xml:space="preserve">
    <value>Kitaplar</value>
  </data>
  <data name="NavFatawa" xml:space="preserve">
    <value>Fetva</value>
  </data>
  <data name="NavContact" xml:space="preserve">
    <value>İletişim</value>
  </data>
  <data name="NavPrivacy" xml:space="preserve">
    <value>Gizlilik</value>
  </data>
  <data name="NavLogin" xml:space="preserve">
    <value>Giriş</value>
  </data>
  
  <!-- Footer -->
  <data name="FooterQuickLinks" xml:space="preserve">
    <value>Hızlı Linkler</value>
  </data>
  <data name="FooterContactInfo" xml:space="preserve">
    <value>İletişim</value>
  </data>
  <data name="FooterRightsReserved" xml:space="preserve">
    <value>Tüm hakları saklıdır.</value>
  </data>
  
  <!-- Common -->
  <data name="Welcome" xml:space="preserve">
    <value>Hoş Geldiniz</value>
  </data>
  <data name="IslamicEducation" xml:space="preserve">
    <value>İslam ilimleri alanında eğitim ve öğretim faaliyetleri.</value>
  </data>
  <data name="SiteDescription" xml:space="preserve">
    <value>Yasin Karataş Hoca ile İslam ilimleri, Kur'an dersleri ve dini eğitim.</value>
  </data>
  
  <!-- Index Page -->
  <data name="FeaturedTopics" xml:space="preserve">
    <value>Öne Çıkan Konular</value>
  </data>
  <data name="TefsirTitle" xml:space="preserve">
    <value>Tefsir</value>
  </data>
  <data name="TefsirSubtitle" xml:space="preserve">
    <value>Kur'an-ı Kerim Tefsiri</value>
  </data>
  <data name="TefsirDescription" xml:space="preserve">
    <value>Kur'an-ı Kerim'in anlamı ve yorumu üzerine detaylı dersler.</value>
  </data>
  <data name="HadisTitle" xml:space="preserve">
    <value>Hadis</value>
  </data>
  <data name="HadisSubtitle" xml:space="preserve">
    <value>Hadis-i Şerif Dersleri</value>
  </data>
  <data name="HadisDescription" xml:space="preserve">
    <value>Peygamber Efendimizin sünnetleri ve hadisleri.</value>
  </data>
  <data name="FikhTitle" xml:space="preserve">
    <value>Fıkıh</value>
  </data>
  <data name="FikhSubtitle" xml:space="preserve">
    <value>İslam Hukuku</value>
  </data>
  <data name="FikhDescription" xml:space="preserve">
    <value>İbadet ve muamelat konularında fıkhi hükümler.</value>
  </data>
  <data name="SiyerTitle" xml:space="preserve">
    <value>Siyer</value>
  </data>
  <data name="SiyerSubtitle" xml:space="preserve">
    <value>Peygamber Efendimizin Hayatı</value>
  </data>
  <data name="SiyerDescription" xml:space="preserve">
    <value>Hz. Muhammed'in (SAV) hayatı ve örnekliği.</value>
  </data>
  <data name="ViewAllLessons" xml:space="preserve">
    <value>Tüm Dersleri Görüntüle</value>
  </data>
  <!-- Contact Page -->
  <data name="contactPageTitle" xml:space="preserve">
    <value>İletişim</value>
  </data>
  <data name="contactPageSubtitle" xml:space="preserve">
    <value>Bizimle iletişime geçin</value>
  </data>
  <data name="contactInfoTitle" xml:space="preserve">
    <value>İletişim Bilgileri</value>
  </data>
  <data name="emailLabel" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="websiteLabel" xml:space="preserve">
    <value>Website</value>
  </data>
  <data name="workingHoursLabel" xml:space="preserve">
    <value>Çalışma Saatleri</value>
  </data>
  <data name="workingHoursTime" xml:space="preserve">
    <value>Pazartesi - Cuma: 09:00 - 18:00</value>
  </data>
  <data name="socialMediaTitle" xml:space="preserve">
    <value>Sosyal Medya</value>
  </data>
  <data name="sendMessageTitle" xml:space="preserve">
    <value>Mesaj Gönderin</value>
  </data>
  <data name="nameLabel" xml:space="preserve">
    <value>Ad Soyad *</value>
  </data>
  <data name="subjectLabel" xml:space="preserve">
    <value>Konu</value>
  </data>
  <data name="messageLabel" xml:space="preserve">
    <value>Mesajınız *</value>
  </data>
  <data name="sendButtonText" xml:space="preserve">
    <value>Mesaj Gönder</value>
  </data>
  <!-- About Page -->
  <data name="aboutPageTitle" xml:space="preserve">
    <value>Yasin Karataş Hoca</value>
  </data>
  <data name="aboutPageSubtitle" xml:space="preserve">
    <value>İslami İlimler Alanında Eğitim ve Öğretim</value>
  </data>
  <data name="lifeEducationTitle" xml:space="preserve">
    <value>Hayatı ve Eğitimi</value>
  </data>
  <data name="aboutParagraph1" xml:space="preserve">
    <value>Yasin Karataş Hoca, İslami ilimler alanında uzun yıllar süren eğitim ve öğretim deneyimine sahiptir. Kur'an-ı Kerim, Hadis-i Şerif, Tefsir ve Fıkıh alanlarında derinlemesine bilgi sahibidir.</value>
  </data>
  <data name="aboutParagraph2" xml:space="preserve">
    <value>İlahiyat Fakültesi mezunu olan hocamız, akademik eğitiminin yanı sıra geleneksel İslami eğitim metodlarını da öğrenmiş ve uygulamıştır. Öğrencilerine hem teorik hem de pratik bilgileri aktarmayı hedeflemektedir.</value>
  </data>
  <data name="aboutParagraph3" xml:space="preserve">
    <value>Günümüz teknolojisini kullanarak İslami ilimleri daha geniş kitlelere ulaştırma konusunda öncü çalışmalar yapmaktadır.</value>
  </data>
  <data name="expertiseTitle" xml:space="preserve">
    <value>Uzmanlık Alanları</value>
  </data>
  <!-- Privacy Page -->
  <data name="privacyPageTitle" xml:space="preserve">
    <value>Gizlilik Politikası</value>
  </data>
  <data name="privacyIntro" xml:space="preserve">
    <value>Bu sayfa yasinkaratas.com.tr web sitesinin gizlilik politikasını açıklamaktadır.</value>
  </data>
  <data name="dataCollectionTitle" xml:space="preserve">
    <value>Toplanan Bilgiler</value>
  </data>
  <data name="dataCollectionText" xml:space="preserve">
    <value>Web sitemizi ziyaret ettiğinizde, kullanıcı deneyimini geliştirmek için bazı bilgiler toplanabilir. Bu bilgiler arasında IP adresiniz, tarayıcı türünüz ve ziyaret ettiğiniz sayfalar yer alır.</value>
  </data>
  <data name="dataUsageTitle" xml:space="preserve">
    <value>Bilgilerin Kullanımı</value>
  </data>
  <data name="dataUsageText" xml:space="preserve">
    <value>Toplanan bilgiler yalnızca site performansını artırmak ve size daha iyi hizmet sunmak amacıyla kullanılır. Kişisel bilgileriniz üçüncü taraflarla paylaşılmaz.</value>
  </data>
  <data name="cookiesTitle" xml:space="preserve">
    <value>Çerezler (Cookies)</value>
  </data>
  <data name="cookiesText" xml:space="preserve">
    <value>Web sitemiz, kullanıcı deneyimini geliştirmek için çerezler kullanabilir. Tarayıcınızdan çerez ayarlarını değiştirebilirsiniz.</value>
  </data>
  <data name="contactForPrivacy" xml:space="preserve">
    <value>Gizlilik politikamız hakkında sorularınız varsa, bizimle iletişime geçebilirsiniz.</value>
  </data>
</root>
