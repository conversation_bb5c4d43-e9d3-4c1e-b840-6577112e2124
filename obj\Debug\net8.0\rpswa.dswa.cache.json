{"GlobalPropertiesHash": "jSceZVp0AdfhYX66PPEo/FaegLY5+S0SlyDLrqjtvBU=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["Nlh4gNBrXSW6r27GOqnnKdKy6TTasiWQqefmDGqsjD8=", "widTVI1AlGCY4AATNlY0svxOVvSpm/zUyhGct9Q+MBU=", "WTRvh7fFcYfsLEomcLub/XwVov9QkssB2cBuClchRNo=", "6wNDqiqXLbW3cnZMeRYAcoZn2xUUMHV7CAkuLwI8rcw=", "67BNGAw/cNbGg4FcwzuXlIAvsl4KlGG4cgACOvnHlno=", "ehoC5/5NCPhAGkg5UPrv3P/Pd8SdG/FQXjnY0zM9ElM=", "zd0PYohpOKj83d5msw5ehQc//Wx6YHsLwo/g25X3IU8=", "BRyCQI4FqN0Dw35DZihU9SnQOn6VnBF2GuGWHvcwxw0=", "YklP3PIaCVrE/L+pgAjcXTBDXaUMyVCAYzHSakY52Kk=", "Yapac5lVvx8NzM3mNpL/FuCPvz8Zbiet3aRi5A8DzSk=", "Pt507w6xcxblTljKtAGAkazo2Nfzfy67v/xsholp7Oo=", "eZDh9zHZv6yVRpf+vR+gdxHTtHu7zMKOlLuXj0aDu1w=", "1ViFMdGeRajetL/twy4JgBQUPdEpBoSpKhPssxL6vus=", "xmgKY2FJqDgHnv/3g/C0JkdVD+z3mJWwG/UYnLfmWDw=", "ZM2vu8vsdZ/jxGR7ybkfjSC69/+NSv/Q9sMcJUoBC9Y=", "5ydv0QEoW0y2B6NV/HBPAgrOIon3OgIfAyKxvA/uJ7A=", "sA+DdlLvVM20YnGJK1W9K5QftaetpfY7nt0wLsWNZwU=", "wG4+bM5cz4eepJD1bP/zqDtqh/L+SAOjtpbRhbbH6WQ=", "Nt32CTbHkF4w22/W6JCCVyQt6NT+Ae4MQnX4H7O7Byo=", "9ABWC5XZRVAT75uDFFzN5uPEakiqyjEYZ0VinY9Cz48=", "Q2jHvnjnYqkDD5fAB15RQctFk2QrxqlYvxNXg7sDyP4=", "w1N0hTzGXWw2YWrztRj4EJv6gYgEPYoCr0HuD7j04ys=", "USOJOtU/1+mATOK75uI4XD7HPN8xAvaOa3ZhPKE1BkU=", "nONa/3iAnjuJNZDYALLSwgr9PNUHHlncpdrtAz1KY2A=", "SZTfbvK22BsFWw3IvCUHPFyLppbnYpgZ0CFgjO3AKxs=", "/UBTfFnXtu/JXzYTmgLjHd9RZG9GUQy2gMJNDDR1Bys=", "kVzW+nVRgveAKzuHVUR8Xoxg1zdmRW2QQMMw4KxXH4U=", "JMMJxI9d+am8ryEzk7YemeGPk5szyf46IwIoO6R/Gjo=", "29lPmHCmBlbLWIJBee4D2MO5Auf4VImrN7VEgMvu87o=", "k+ANf5ifvqQF/F9kbUcWBCxqwRZ6Hk3IU1bB3EjKP9M=", "4Bzf7YsUOSNbAhIOavtpe56WmxEMQXdsVSZg2SPm7K0=", "gXyxoBe7eumk1U/tOjt9SmeSEtTse6R81GVgqV6Chqc=", "bBOfxqBKRYRP1zNXsO0NmJeVtONAesLxs+gn+SVNc6Y=", "UiV4zZjd72BlwyYO2zvVQGZWVd4keeBPhmuumCG5Tok=", "Vg3GMU6swQGmW+a5eiwvSWB/g6rN0jIs3RBsPddKQy8=", "zZQEtm0MhOumPxoBZqzjibjMU3dJO9huYz4QH9ZNCQw=", "hiXFHHg5m2CswV3rknQhte7Q3XkmJJ1mF7ZWvGiPatc=", "hNqlYhQkA5g8LuqgH1zLDktx8hicExov1vodkYjixaA=", "pal8IzalxnRKtffCjOs68IGUMqsebcP7QecYabJKg6Y=", "d03Q9/rzxYwsywRIFDWiMjVLQevbtHci8AMGgNd0NjA=", "N3i+cD2fy6O7UxXjAJSmmuqVTBgZb3XOGqOmJR/maOI=", "FgYa5qAAxCZ9XHyB6d5RRWKwArj7SeySnvVo5iECFmo=", "LX+rdmY94QyNMoZTrAVn6XJlbBeP57lCa0HThszxdQU=", "oZYkBpp9bkbHTviAVB9rGqa9Ce6lGVMZTmwwgr1WhuE=", "6Z1z8DfyIF6vfb36QppcZW1IaYr0rcfBRwpZ19F5LSk=", "f0UUyYkOU+NbnmP34z10x7Ti00kAc1SEo0zuf7Djnog=", "2zM2faL0Zgm59NAThSeZuAPjf2pS/e6y1CJp7fif85w=", "IZkkUHbZlHpK1hq482lcwRD6k1DTaX1ZVOZEhBG3Bsw=", "xqPJYClZ7dX04GCcsccDk8ttkTXyG5p9RRAHIuj5ugo=", "EhhyUmT3eB6tJLlBukCQg/lLtujP58+2JJmMBeV/Y7c=", "eXF/4HawPxF4qWB4hpo/AC8xb7bv6L8jbMMDbRQi7eU=", "16bC8sBMYkTpOy+6NC6jaz0ap/yEj5MCUtgqVL43ZW4=", "gPy1perzevBytM/QcKODZ+XBBYdF28ox/GGfjQESE3I=", "mk5LZHEYmn7dhAcyA8vvHEJAmKEZebayijL5fIHzjxs=", "E99b4LGHjwYAevbCq9zguiNsqyZhb50ZTjfGnoBwAW8=", "/o4Hx86+QuHBW+jlE3qMToWSygtJVw8I1q+koBSFTIQ=", "aEJF4+sjH43rwiHyrF8Midjg6UEXUgnVxa3SuV9ponk=", "bwedQM2F5qDKPGjb7Y6UYXMpfBMyupcm3QeoxVeHH54=", "JUBadHyhycXlajE/yhyMTV8mwrZvZfMF8roCTyyiWLE=", "3vhv6UJ3iWxTe5eB9Ouxmg+piz4ZNMrS5HkDpIR9KgE=", "8A2suN5sQ6pTkkRUq1rVFAtT22nlrog+BQR4VctevoA=", "tDYzwDx1Gw17flV302ZOa1oZMBrv0g3QgzPwOj5xHoA=", "OX133aZs8LT149dTZM3nWETz/L+Ykn0NUkyUMsdRfVQ=", "zx6dc/V4JxGshpEUxbsjx3W23F0fSy2TkDQ+svSSDlk=", "hLuz0MZEpx6YmlSAgjgJtW9ODDAs3GeAVZ4RXk3lJQU=", "4601tK+kzblCfL2M2RpdC+8ZQfkfOI7v3nEzesetXNs=", "VJ1B4hbagsQ6b+z0fRtEZmQVV08l5Egync7KRbL31ds=", "CyLj5A4dl7AdlVA0nx5seaEKvcX8MwomthmYaRWianM=", "7ScyNABxHpzzYZ1aUIBBtWCf9+denVNKcS5nMP+DgnQ=", "LfIES4Sx8EfNxRHjwuuMW0N6ZWv6YSD3bkck3bHxOME=", "jgw9tPgSYCJU3fg1f91LMs6j4SoIlrkTP4yMym4FoYI=", "tIxkLWxCRBzV9cZt3VUkxbz5PapU1DeXOoXgOJDE6zI=", "z3i/3oDDHyHvzv9MwoD8bIDEny5atKqo6DemQadFQz8=", "OQLRjsT9bpCkwAlQZTcRzlL1bJgDXfv7Nb8cOPsCBNc=", "swrxjw7qmAoA2xZToAaLyvJ4wuae42WimTqc2p3V3d8=", "Nphsx0cJYE/x8YB2bdTfKKLjXJApkk+7cZHHp73eUuc="], "CachedAssets": {"Nlh4gNBrXSW6r27GOqnnKdKy6TTasiWQqefmDGqsjD8=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\css\\site.css", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2tiyv64ts", "Integrity": "pAGv4ietcJNk/EwsQZ5BN9+K4MuNYS2a9wl4Jw+q9D0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 362, "LastWriteTime": "2025-06-21T17:13:12.2501685+00:00"}, "widTVI1AlGCY4AATNlY0svxOVvSpm/zUyhGct9Q+MBU=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\favicon.ico", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-06-21T17:13:12.4958766+00:00"}, "WTRvh7fFcYfsLEomcLub/XwVov9QkssB2cBuClchRNo=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\js\\site.js", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-06-21T17:13:12.2511614+00:00"}, "6wNDqiqXLbW3cnZMeRYAcoZn2xUUMHV7CAkuLwI8rcw=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "agp80tu62r", "Integrity": "JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70538, "LastWriteTime": "2025-06-21T17:13:12.3107624+00:00"}, "67BNGAw/cNbGg4FcwzuXlIAvsl4KlGG4cgACOvnHlno=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "st1cbwfwo5", "Integrity": "QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 196535, "LastWriteTime": "2025-06-21T17:13:12.3117606+00:00"}, "ehoC5/5NCPhAGkg5UPrv3P/Pd8SdG/FQXjnY0zM9ElM=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "unj9p35syc", "Integrity": "ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51319, "LastWriteTime": "2025-06-21T17:13:12.3117606+00:00"}, "zd0PYohpOKj83d5msw5ehQc//Wx6YHsLwo/g25X3IU8=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5vj65cig9w", "Integrity": "72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 117439, "LastWriteTime": "2025-06-21T17:13:12.3127615+00:00"}, "BRyCQI4FqN0Dw35DZihU9SnQOn6VnBF2GuGWHvcwxw0=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q2ku51ktnl", "Integrity": "3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70612, "LastWriteTime": "2025-06-21T17:13:12.3127615+00:00"}, "YklP3PIaCVrE/L+pgAjcXTBDXaUMyVCAYzHSakY52Kk=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2q4vfeazbq", "Integrity": "qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 196539, "LastWriteTime": "2025-06-21T17:13:12.3784952+00:00"}, "Yapac5lVvx8NzM3mNpL/FuCPvz8Zbiet3aRi5A8DzSk=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "n1oizzvkh6", "Integrity": "O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51394, "LastWriteTime": "2025-06-21T17:13:12.3784952+00:00"}, "Pt507w6xcxblTljKtAGAkazo2Nfzfy67v/xsholp7Oo=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o371a8zbv2", "Integrity": "NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 117516, "LastWriteTime": "2025-06-21T17:13:12.3784952+00:00"}, "eZDh9zHZv6yVRpf+vR+gdxHTtHu7zMKOlLuXj0aDu1w=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7na4sro3qu", "Integrity": "4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 5850, "LastWriteTime": "2025-06-21T17:13:12.3795008+00:00"}, "1ViFMdGeRajetL/twy4JgBQUPdEpBoSpKhPssxL6vus=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jeal3x0ldm", "Integrity": "FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 105138, "LastWriteTime": "2025-06-21T17:13:12.3795008+00:00"}, "xmgKY2FJqDgHnv/3g/C0JkdVD+z3mJWwG/UYnLfmWDw=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f8imaxxbri", "Integrity": "z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 4646, "LastWriteTime": "2025-06-21T17:13:12.3805016+00:00"}, "ZM2vu8vsdZ/jxGR7ybkfjSC69/+NSv/Q9sMcJUoBC9Y=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "okkk44j0xs", "Integrity": "2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 35330, "LastWriteTime": "2025-06-21T17:13:12.3810083+00:00"}, "5ydv0QEoW0y2B6NV/HBPAgrOIon3OgIfAyKxvA/uJ7A=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0wve5yxp74", "Integrity": "8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 5827, "LastWriteTime": "2025-06-21T17:13:12.3810083+00:00"}, "sA+DdlLvVM20YnGJK1W9K5QftaetpfY7nt0wLsWNZwU=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cwzlr5n8x4", "Integrity": "/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 105151, "LastWriteTime": "2025-06-21T17:13:12.3820204+00:00"}, "wG4+bM5cz4eepJD1bP/zqDtqh/L+SAOjtpbRhbbH6WQ=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "npxfuf8dg6", "Integrity": "a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 4718, "LastWriteTime": "2025-06-21T17:13:12.3830187+00:00"}, "Nt32CTbHkF4w22/W6JCCVyQt6NT+Ae4MQnX4H7O7Byo=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wmug9u23qg", "Integrity": "GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 41570, "LastWriteTime": "2025-06-21T17:13:12.3830187+00:00"}, "9ABWC5XZRVAT75uDFFzN5uPEakiqyjEYZ0VinY9Cz48=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tey0rigmnh", "Integrity": "NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 71584, "LastWriteTime": "2025-06-21T17:13:12.3850168+00:00"}, "Q2jHvnjnYqkDD5fAB15RQctFk2QrxqlYvxNXg7sDyP4=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j75<PERSON><PERSON><PERSON>", "Integrity": "4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 192271, "LastWriteTime": "2025-06-21T17:13:12.3885396+00:00"}, "w1N0hTzGXWw2YWrztRj4EJv6gYgEPYoCr0HuD7j04ys=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "16095smhkz", "Integrity": "5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 53479, "LastWriteTime": "2025-06-21T17:13:12.3895406+00:00"}, "USOJOtU/1+mATOK75uI4XD7HPN8xAvaOa3ZhPKE1BkU=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vy0bq9ydhf", "Integrity": "p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 111875, "LastWriteTime": "2025-06-21T17:13:12.3905411+00:00"}, "nONa/3iAnjuJNZDYALLSwgr9PNUHHlncpdrtAz1KY2A=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b4skse8du6", "Integrity": "peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 71451, "LastWriteTime": "2025-06-21T17:13:12.39154+00:00"}, "SZTfbvK22BsFWw3IvCUHPFyLppbnYpgZ0CFgjO3AKxs=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ab1c3rmv7g", "Integrity": "puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 192214, "LastWriteTime": "2025-06-21T17:13:12.392541+00:00"}, "/UBTfFnXtu/JXzYTmgLjHd9RZG9GUQy2gMJNDDR1Bys=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u3xrusw2ol", "Integrity": "Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 53407, "LastWriteTime": "2025-06-21T17:13:12.392541+00:00"}, "kVzW+nVRgveAKzuHVUR8Xoxg1zdmRW2QQMMw4KxXH4U=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "56d2bn4wt9", "Integrity": "02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 111710, "LastWriteTime": "2025-06-21T17:13:12.3935418+00:00"}, "JMMJxI9d+am8ryEzk7YemeGPk5szyf46IwIoO6R/Gjo=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mpyigms19s", "Integrity": "xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 204136, "LastWriteTime": "2025-06-21T17:13:12.3945415+00:00"}, "29lPmHCmBlbLWIJBee4D2MO5Auf4VImrN7VEgMvu87o=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "73kdqttayv", "Integrity": "DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 536547, "LastWriteTime": "2025-06-21T17:13:12.3945415+00:00"}, "k+ANf5ifvqQF/F9kbUcWBCxqwRZ6Hk3IU1bB3EjKP9M=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-06-21T17:13:12.3960491+00:00"}, "4Bzf7YsUOSNbAhIOavtpe56WmxEMQXdsVSZg2SPm7K0=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-06-21T17:13:12.3990659+00:00"}, "gXyxoBe7eumk1U/tOjt9SmeSEtTse6R81GVgqV6Chqc=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ve6x09088i", "Integrity": "SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 203803, "LastWriteTime": "2025-06-21T17:13:12.4000654+00:00"}, "bBOfxqBKRYRP1zNXsO0NmJeVtONAesLxs+gn+SVNc6Y=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4gxs3k148c", "Integrity": "VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 536461, "LastWriteTime": "2025-06-21T17:13:12.406067+00:00"}, "UiV4zZjd72BlwyYO2zvVQGZWVd4keeBPhmuumCG5Tok=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9b9oa1qrmt", "Integrity": "22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 162825, "LastWriteTime": "2025-06-21T17:13:12.4085914+00:00"}, "Vg3GMU6swQGmW+a5eiwvSWB/g6rN0jIs3RBsPddKQy8=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fctod5rc9n", "Integrity": "j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 661035, "LastWriteTime": "2025-06-21T17:13:12.4095916+00:00"}, "zZQEtm0MhOumPxoBZqzjibjMU3dJO9huYz4QH9ZNCQw=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l2av4jpuoj", "Integrity": "vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 208492, "LastWriteTime": "2025-06-21T17:13:12.4105908+00:00"}, "hiXFHHg5m2CswV3rknQhte7Q3XkmJJ1mF7ZWvGiPatc=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbynt5jhd9", "Integrity": "gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 425643, "LastWriteTime": "2025-06-21T17:13:12.4131074+00:00"}, "hNqlYhQkA5g8LuqgH1zLDktx8hicExov1vodkYjixaA=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "25iw1kog22", "Integrity": "KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 78468, "LastWriteTime": "2025-06-21T17:13:12.4131074+00:00"}, "pal8IzalxnRKtffCjOs68IGUMqsebcP7QecYabJKg6Y=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2nslu3uf3", "Integrity": "xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 327261, "LastWriteTime": "2025-06-21T17:13:12.4141078+00:00"}, "d03Q9/rzxYwsywRIFDWiMjVLQevbtHci8AMGgNd0NjA=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m39kt2b5c9", "Integrity": "EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 139019, "LastWriteTime": "2025-06-21T17:13:12.4161074+00:00"}, "N3i+cD2fy6O7UxXjAJSmmuqVTBgZb3XOGqOmJR/maOI=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2lgwfvgpvi", "Integrity": "CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 288320, "LastWriteTime": "2025-06-21T17:13:12.4176236+00:00"}, "FgYa5qAAxCZ9XHyB6d5RRWKwArj7SeySnvVo5iECFmo=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "um2aeqy4ik", "Integrity": "Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 72016, "LastWriteTime": "2025-06-21T17:13:12.4186317+00:00"}, "LX+rdmY94QyNMoZTrAVn6XJlbBeP57lCa0HThszxdQU=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wsezl0heh6", "Integrity": "sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222508, "LastWriteTime": "2025-06-21T17:13:12.419631+00:00"}, "oZYkBpp9bkbHTviAVB9rGqa9Ce6lGVMZTmwwgr1WhuE=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o4kw7cc6tf", "Integrity": "6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 148168, "LastWriteTime": "2025-06-21T17:13:12.419631+00:00"}, "6Z1z8DfyIF6vfb36QppcZW1IaYr0rcfBRwpZ19F5LSk=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6<PERSON><PERSON><PERSON><PERSON>bh", "Integrity": "Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 289522, "LastWriteTime": "2025-06-21T17:13:12.4206319+00:00"}, "f0UUyYkOU+NbnmP34z10x7Ti00kAc1SEo0zuf7Djnog=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zwph15dxgs", "Integrity": "c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 59511, "LastWriteTime": "2025-06-21T17:13:12.4216323+00:00"}, "2zM2faL0Zgm59NAThSeZuAPjf2pS/e6y1CJp7fif85w=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u33ctipx7g", "Integrity": "ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 217145, "LastWriteTime": "2025-06-21T17:13:12.4226318+00:00"}, "IZkkUHbZlHpK1hq482lcwRD6k1DTaX1ZVOZEhBG3Bsw=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-06-21T17:13:12.4707878+00:00"}, "xqPJYClZ7dX04GCcsccDk8ttkTXyG5p9RRAHIuj5ugo=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-06-21T17:13:12.4963832+00:00"}, "EhhyUmT3eB6tJLlBukCQg/lLtujP58+2JJmMBeV/Y7c=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-06-21T17:13:12.497392+00:00"}, "eXF/4HawPxF4qWB4hpo/AC8xb7bv6L8jbMMDbRQi7eU=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-06-21T17:13:12.497392+00:00"}, "16bC8sBMYkTpOy+6NC6jaz0ap/yEj5MCUtgqVL43ZW4=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ay5nd8zt9x", "Integrity": "4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 52977, "LastWriteTime": "2025-06-21T17:13:12.2556664+00:00"}, "gPy1perzevBytM/QcKODZ+XBBYdF28ox/GGfjQESE3I=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9oaff4kq20", "Integrity": "N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22177, "LastWriteTime": "2025-06-21T17:13:12.2561864+00:00"}, "mk5LZHEYmn7dhAcyA8vvHEJAmKEZebayijL5fIHzjxs=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pzqfkb6aqo", "Integrity": "m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 51171, "LastWriteTime": "2025-06-21T17:13:12.2561864+00:00"}, "E99b4LGHjwYAevbCq9zguiNsqyZhb50ZTjfGnoBwAW8=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7iojwaux1", "Integrity": "JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 24601, "LastWriteTime": "2025-06-21T17:13:12.257196+00:00"}, "/o4Hx86+QuHBW+jlE3qMToWSygtJVw8I1q+koBSFTIQ=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-06-21T17:13:12.4948778+00:00"}, "aEJF4+sjH43rwiHyrF8Midjg6UEXUgnVxa3SuV9ponk=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fwhahm2icz", "Integrity": "H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 288580, "LastWriteTime": "2025-06-21T17:13:12.2531599+00:00"}, "bwedQM2F5qDKPGjb7Y6UYXMpfBMyupcm3QeoxVeHH54=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dd6z7egasc", "Integrity": "/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 89501, "LastWriteTime": "2025-06-21T17:13:12.2531599+00:00"}, "JUBadHyhycXlajE/yhyMTV8mwrZvZfMF8roCTyyiWLE=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5pze98is44", "Integrity": "OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 137972, "LastWriteTime": "2025-06-21T17:13:12.2541603+00:00"}, "3vhv6UJ3iWxTe5eB9Ouxmg+piz4ZNMrS5HkDpIR9KgE=": {"Identity": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "yasinkaratas.com.tr", "SourceType": "Discovered", "ContentRoot": "D:\\Yasin\\yasinkaratas.com.tr\\wwwroot\\", "BasePath": "_content/yasinkaratas.com.tr", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-06-21T17:13:12.4918783+00:00"}}, "CachedCopyCandidates": {}}