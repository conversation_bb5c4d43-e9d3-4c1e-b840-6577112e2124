using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using yasinkaratas.com.tr.Models;
using yasinkaratas.com.tr.Resources;

namespace yasinkaratas.com.tr.Controllers;

public class HomeController : BaseController
{
    private readonly ILogger<HomeController> _logger;

    public HomeController(ILogger<HomeController> logger, IStringLocalizer<SharedResources> localizer) 
        : base(localizer)
    {
        _logger = logger;
    }

    public IActionResult Index()
    {
        // Debug: Test localization
        ViewBag.TestMessage = _localizer["NavHome"];
        ViewBag.CurrentCulture = System.Globalization.CultureInfo.CurrentCulture.Name;
        ViewBag.CurrentUICulture = System.Globalization.CultureInfo.CurrentUICulture.Name;
        
        return View();
    }

    public IActionResult TestLocalization()
    {
        var culture = System.Globalization.CultureInfo.CurrentCulture.Name;
        var uiCulture = System.Globalization.CultureInfo.CurrentUICulture.Name;
        var navHome = _localizer["NavHome"];
        
        return Json(new { 
            Culture = culture, 
            UICulture = uiCulture, 
            NavHome = navHome.Value,
            NavHomeResourceNotFound = navHome.ResourceNotFound
        });
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
