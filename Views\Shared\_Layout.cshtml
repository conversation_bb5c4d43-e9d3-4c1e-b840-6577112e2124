﻿@using Microsoft.Extensions.Localization
@inject IStringLocalizer<yasinkaratas.com.tr.Resources.SharedResources> Localizer

<!DOCTYPE html>
<html lang="@(System.Threading.Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName)" dir="@(System.Threading.Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName == "ar" ? "rtl" : "ltr")">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="@ViewBag.MetaDescription" />
    <meta name="keywords" content="@ViewBag.MetaKeywords" />
    <meta name="author" content="Ya<PERSON> Kara<PERSON> Hoca" />
    <meta name="robots" content="index, follow" />
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="@ViewBag.Title" />
    <meta property="og:description" content="@ViewBag.MetaDescription" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="@ViewContext.HttpContext?.Request.Path" />
    <meta property="og:site_name" content="Yasin Karataş Hoca" />
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="@ViewBag.Title" />
    <meta name="twitter:description" content="@ViewBag.MetaDescription" />
    
    <!-- Canonical URL -->
    <link rel="canonical" href="@(ViewContext.HttpContext?.Request.IsHttps == true ? $"https://{ViewContext.HttpContext?.Request.Host}{ViewContext.HttpContext?.Request.Path}" : $"http://{ViewContext.HttpContext?.Request.Host}{ViewContext.HttpContext?.Request.Path}")" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="/Content/Site.css" rel="stylesheet" />
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Responsive CSS -->
    <style>
        @@media (max-width: 768px) {
            .desktop-nav {
                display: none !important;
            }
            #mobile-menu-toggle {
                display: block !important;
            }
        }
        
        @@media (min-width: 769px) {
            .desktop-nav {
                display: flex !important;
            }
            #mobile-menu-toggle {
                display: none !important;
            }
            .mobile-nav {
                display: none !important;
            }
        }
        
        /* Ensure menu fits in one line on all desktop sizes */
        @@media (max-width: 1200px) and (min-width: 769px) {
            .desktop-nav ul li {
                margin: 0 4px !important;
            }
            .desktop-nav ul li a {
                font-size: 12px !important;
                padding: 3px 5px !important;
            }
        }
        
        @@media (max-width: 1000px) and (min-width: 769px) {
            .desktop-nav ul li {
                margin: 0 3px !important;
            }
            .desktop-nav ul li a {
                font-size: 11px !important;
                padding: 2px 4px !important;
            }
        }
        
        /* Language Dropdown Styling */
        .language-dropdown:hover {
            background: rgba(255,255,255,0.2) !important;
        }
        
        @@media (max-width: 768px) {
            .language-dropdown {
                margin-right: 10px !important;
            }
        }
        
        /* RTL Support for Arabic */
        html[dir="rtl"] {
            text-align: right;
        }
        
        html[dir="rtl"] .desktop-nav ul {
            flex-direction: row-reverse;
        }
        
        html[dir="rtl"] .language-dropdown {
            margin-left: 15px;
            margin-right: 0;
        }
        
        html[dir="rtl"] .site-header > .container > .row > .col-md-12 > div {
            flex-direction: row-reverse;
        }
    </style>
</head>
<body>
    <!-- Header - Responsive tek satır -->
    <header class="site-header" style="background: #2c3e50; padding: 15px 0; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div style="display: flex; justify-content: space-between; align-items: center; position: relative;">
                        <!-- Logo -->
                        <div class="site-logo">
                            <a href="/" style="text-decoration: none;">
                                <h2 style="color: white; font-size: 24px; font-weight: 700; margin: 0;">Yasin Karataş</h2>
                            </a>
                        </div>
                        
                        <!-- Desktop Navigation Menu -->
                        <nav class="desktop-nav" style="display: flex;">
                            <ul style="margin: 0; padding: 0; list-style: none; display: flex; align-items: center;">
                                <li style="margin: 0 6px;"><a href="/" style="color: #ffffff; text-decoration: none; font-size: 13px; font-weight: 500; padding: 4px 6px; border-radius: 3px; transition: all 0.3s; white-space: nowrap;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">@Localizer["NavHome"]</a></li>
                                <li style="margin: 0 6px;"><a href="/Home/About" style="color: #ffffff; text-decoration: none; font-size: 13px; font-weight: 500; padding: 4px 6px; border-radius: 3px; transition: all 0.3s; white-space: nowrap;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">@Localizer["NavAbout"]</a></li>
                                <li style="margin: 0 6px;"><a href="/Home/Quran" style="color: #ffffff; text-decoration: none; font-size: 13px; font-weight: 500; padding: 4px 6px; border-radius: 3px; transition: all 0.3s; white-space: nowrap;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">@Localizer["NavQuran"]</a></li>
                                <li style="margin: 0 6px;"><a href="/Home/Lessons" style="color: #ffffff; text-decoration: none; font-size: 13px; font-weight: 500; padding: 4px 6px; border-radius: 3px; transition: all 0.3s; white-space: nowrap;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">@Localizer["NavLessons"]</a></li>
                                <li style="margin: 0 6px;"><a href="/Home/Videos" style="color: #ffffff; text-decoration: none; font-size: 13px; font-weight: 500; padding: 4px 6px; border-radius: 3px; transition: all 0.3s; white-space: nowrap;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">@Localizer["NavVideos"]</a></li>
                                <li style="margin: 0 6px;"><a href="/Home/Articles" style="color: #ffffff; text-decoration: none; font-size: 13px; font-weight: 500; padding: 4px 6px; border-radius: 3px; transition: all 0.3s; white-space: nowrap;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">@Localizer["NavArticles"]</a></li>
                                <li style="margin: 0 6px;"><a href="/Home/Books" style="color: #ffffff; text-decoration: none; font-size: 13px; font-weight: 500; padding: 4px 6px; border-radius: 3px; transition: all 0.3s; white-space: nowrap;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">@Localizer["NavBooks"]</a></li>
                                <li style="margin: 0 6px;"><a href="/Home/Fatawa" style="color: #ffffff; text-decoration: none; font-size: 13px; font-weight: 500; padding: 4px 6px; border-radius: 3px; transition: all 0.3s; white-space: nowrap;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">@Localizer["NavFatawa"]</a></li>
                                <li style="margin: 0 6px;"><a href="/Home/Contact" style="color: #ffffff; text-decoration: none; font-size: 13px; font-weight: 500; padding: 4px 6px; border-radius: 3px; transition: all 0.3s; white-space: nowrap;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">@Localizer["NavContact"]</a></li>
                                <li style="margin: 0 6px;"><a href="/Home/Privacy" style="color: #ffffff; text-decoration: none; font-size: 13px; font-weight: 500; padding: 4px 6px; border-radius: 3px; transition: all 0.3s; white-space: nowrap;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">@Localizer["NavPrivacy"]</a></li>
                            </ul>
                        </nav>
                        
                        <!-- Language & Login + Mobile Menu Toggle -->
                        <div style="display: flex; align-items: center; color: white; font-size: 12px;">
                            <!-- Language Dropdown -->
                            <div class="language-dropdown" style="margin-right: 15px; position: relative; cursor: pointer; border: 1px solid rgba(255,255,255,0.3); border-radius: 4px; padding: 5px 8px; background: rgba(255,255,255,0.1);" onclick="toggleLanguageMenu()">
                                <div style="display: flex; align-items: center;">
                                    <i class="fas fa-globe"></i>
                                    <span id="current-lang" style="margin-left: 5px; margin-right: 5px; font-weight: 500;">@(System.Threading.Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName.ToUpper())</span>
                                    <i class="fas fa-chevron-down" style="font-size: 10px; transition: transform 0.3s ease;"></i>
                                </div>
                                <div id="language-menu" style="display: none; position: absolute; top: 100%; left: 0; background: #2c3e50; border: 1px solid rgba(255,255,255,0.2); border-radius: 3px; z-index: 1000; min-width: 100px; box-shadow: 0 4px 8px rgba(0,0,0,0.3); margin-top: 2px;">
                                    <a href="?culture=tr" style="display: block; padding: 10px 12px; color: white; text-decoration: none; border-bottom: 1px solid rgba(255,255,255,0.1); font-size: 13px;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">🇹🇷 Türkçe</a>
                                    <a href="?culture=en" style="display: block; padding: 10px 12px; color: white; text-decoration: none; border-bottom: 1px solid rgba(255,255,255,0.1); font-size: 13px;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">🇺🇸 English</a>
                                    <a href="?culture=ar" style="display: block; padding: 10px 12px; color: white; text-decoration: none; border-bottom: 1px solid rgba(255,255,255,0.1); font-size: 13px;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">🇸🇦 العربية</a>
                                    <a href="?culture=de" style="display: block; padding: 10px 12px; color: white; text-decoration: none; border-bottom: 1px solid rgba(255,255,255,0.1); font-size: 13px;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">🇩🇪 Deutsch</a>
                                    <a href="?culture=ru" style="display: block; padding: 10px 12px; color: white; text-decoration: none; font-size: 13px;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">🇷🇺 Русский</a>
                                </div>
                            </div>
                            <div style="margin-right: 15px; cursor: pointer;">
                                <i class="fas fa-user"></i> <span>@Localizer["NavLogin"]</span>
                            </div>
                            <!-- Mobile Menu Toggle -->
                            <button id="mobile-menu-toggle" style="background: none; border: none; color: white; font-size: 18px; cursor: pointer; display: none;" onclick="toggleMobileMenu()">
                                <i class="fas fa-bars"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Mobile Navigation Menu -->
                    <nav id="mobile-nav" class="mobile-nav" style="display: none; margin-top: 15px; border-top: 1px solid rgba(255,255,255,0.2); padding-top: 15px;">
                        <ul style="margin: 0; padding: 0; list-style: none; display: flex; flex-direction: column; gap: 10px;">
                            <li><a href="/" style="color: #ffffff; text-decoration: none; font-size: 16px; font-weight: 500; padding: 8px 15px; border-radius: 3px; transition: all 0.3s; display: block;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">@Localizer["NavHome"]</a></li>
                            <li><a href="/Home/About" style="color: #ffffff; text-decoration: none; font-size: 16px; font-weight: 500; padding: 8px 15px; border-radius: 3px; transition: all 0.3s; display: block;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">@Localizer["NavAbout"]</a></li>
                            <li><a href="/Home/Quran" style="color: #ffffff; text-decoration: none; font-size: 16px; font-weight: 500; padding: 8px 15px; border-radius: 3px; transition: all 0.3s; display: block;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">@Localizer["NavQuran"]</a></li>
                            <li><a href="/Home/Lessons" style="color: #ffffff; text-decoration: none; font-size: 16px; font-weight: 500; padding: 8px 15px; border-radius: 3px; transition: all 0.3s; display: block;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">@Localizer["NavLessons"]</a></li>
                            <li><a href="/Home/Videos" style="color: #ffffff; text-decoration: none; font-size: 16px; font-weight: 500; padding: 8px 15px; border-radius: 3px; transition: all 0.3s; display: block;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">@Localizer["NavVideos"]</a></li>
                            <li><a href="/Home/Articles" style="color: #ffffff; text-decoration: none; font-size: 16px; font-weight: 500; padding: 8px 15px; border-radius: 3px; transition: all 0.3s; display: block;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">@Localizer["NavArticles"]</a></li>
                            <li><a href="/Home/Books" style="color: #ffffff; text-decoration: none; font-size: 16px; font-weight: 500; padding: 8px 15px; border-radius: 3px; transition: all 0.3s; display: block;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">@Localizer["NavBooks"]</a></li>
                            <li><a href="/Home/Fatawa" style="color: #ffffff; text-decoration: none; font-size: 16px; font-weight: 500; padding: 8px 15px; border-radius: 3px; transition: all 0.3s; display: block;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">@Localizer["NavFatawa"]</a></li>
                            <li><a href="/Home/Contact" style="color: #ffffff; text-decoration: none; font-size: 16px; font-weight: 500; padding: 8px 15px; border-radius: 3px; transition: all 0.3s; display: block;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">@Localizer["NavContact"]</a></li>
                            <li><a href="/Home/Privacy" style="color: #ffffff; text-decoration: none; font-size: 16px; font-weight: 500; padding: 8px 15px; border-radius: 3px; transition: all 0.3s; display: block;" onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'" onmouseout="this.style.backgroundColor='transparent'">@Localizer["NavPrivacy"]</a></li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        @RenderBody()
    </main>

    <!-- Footer -->
    <footer class="site-footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <div class="footer-section">
                        <h5>Yasin Karataş Hoca</h5>
                        <p>@Localizer["IslamicEducation"]</p>
                        <div class="social-links">
                            <a href="#"><i class="fab fa-facebook-f"></i></a>
                            <a href="#"><i class="fab fa-twitter"></i></a>
                            <a href="#"><i class="fab fa-youtube"></i></a>
                            <a href="#"><i class="fab fa-instagram"></i></a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="footer-section">
                        <h5>@Localizer["FooterQuickLinks"]</h5>
                        <ul class="footer-links">
                            <li><a href="/">@Localizer["NavHome"]</a></li>
                            <li><a href="/Home/About">@Localizer["NavAbout"]</a></li>
                            <li><a href="/Home/Lessons">@Localizer["NavLessons"]</a></li>
                            <li><a href="/Home/Articles">@Localizer["NavArticles"]</a></li>
                            <li><a href="/Home/Contact">@Localizer["NavContact"]</a></li>
                            <li><a href="/Home/Privacy">@Localizer["NavPrivacy"]</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="footer-section">
                        <h5>@Localizer["FooterContactInfo"]</h5>
                        <div class="contact-info">
                            <p><i class="fas fa-globe"></i> www.yasinkaratas.com.tr</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="row">
                    <div class="col-md-12 text-center">
                        <p>&copy; @DateTime.Now.Year Yasin Karataş Hoca. @Localizer["FooterRightsReserved"] (Ver. 2.2.0)</p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/js/bootstrap.min.js"></script>
    @RenderSection("scripts", required: false)
    
    <!-- Mobile Menu Toggle Script -->
    <script>
        function toggleMobileMenu() {
            var mobileNav = document.getElementById('mobile-nav');
            var toggle = document.getElementById('mobile-menu-toggle');
            var icon = toggle.querySelector('i');
            
            if (mobileNav.style.display === 'none' || mobileNav.style.display === '') {
                mobileNav.style.display = 'block';
                icon.classList.remove('fa-bars');
                icon.classList.add('fa-times');
            } else {
                mobileNav.style.display = 'none';
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            }
        }
        
        function toggleLanguageMenu() {
            var languageMenu = document.getElementById('language-menu');
            var chevron = document.querySelector('.language-dropdown .fa-chevron-down');
            
            if (languageMenu.style.display === 'none' || languageMenu.style.display === '') {
                languageMenu.style.display = 'block';
                chevron.style.transform = 'rotate(180deg)';
            } else {
                languageMenu.style.display = 'none';
                chevron.style.transform = 'rotate(0deg)';
            }
        }
        
        function selectLanguage(lang) {
            var languageMenu = document.getElementById('language-menu');
            var chevron = document.querySelector('.language-dropdown .fa-chevron-down');
            
            languageMenu.style.display = 'none';
            chevron.style.transform = 'rotate(0deg)';
        }
        
        // Close mobile menu when clicking on a link
        document.addEventListener('DOMContentLoaded', function() {
            var mobileLinks = document.querySelectorAll('.mobile-nav a');
            mobileLinks.forEach(function(link) {
                link.addEventListener('click', function() {
                    toggleMobileMenu();
                });
            });
            
            // Close language menu when clicking outside
            document.addEventListener('click', function(event) {
                var languageDropdown = document.querySelector('.language-dropdown');
                var languageMenu = document.getElementById('language-menu');
                
                if (!languageDropdown.contains(event.target)) {
                    languageMenu.style.display = 'none';
                }
            });
        });
    </script>

</body>
</html>
