<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Navigation -->
  <data name="NavHome" xml:space="preserve">
    <value>Главная</value>
  </data>
  <data name="NavAbout" xml:space="preserve">
    <value>О шейхе</value>
  </data>
  <data name="NavQuran" xml:space="preserve">
    <value>Коран</value>
  </data>
  <data name="NavLessons" xml:space="preserve">
    <value>Уроки</value>
  </data>
  <data name="NavVideos" xml:space="preserve">
    <value>Видео</value>
  </data>
  <data name="NavArticles" xml:space="preserve">
    <value>Статьи</value>
  </data>
  <data name="NavBooks" xml:space="preserve">
    <value>Книги</value>
  </data>
  <data name="NavFatawa" xml:space="preserve">
    <value>Фетвы</value>
  </data>
  <data name="NavContact" xml:space="preserve">
    <value>Контакты</value>
  </data>
  <data name="NavPrivacy" xml:space="preserve">
    <value>Конфиденциальность</value>
  </data>
  <data name="NavLogin" xml:space="preserve">
    <value>Войти</value>
  </data>
  
  <!-- Footer -->
  <data name="FooterQuickLinks" xml:space="preserve">
    <value>Быстрые ссылки</value>
  </data>
  <data name="FooterContactInfo" xml:space="preserve">
    <value>Контактная информация</value>
  </data>
  <data name="FooterRightsReserved" xml:space="preserve">
    <value>Все права защищены.</value>
  </data>
  <data name="FooterCopyright" xml:space="preserve">
    <value>© 2024 Ясин Караташ. Все права защищены.</value>
  </data>
  
  <!-- Homepage Content -->
  <data name="WelcomeTitle" xml:space="preserve">
    <value>Добро пожаловать</value>
  </data>
  <data name="IslamicEducation" xml:space="preserve">
    <value>Исламское образование и преподавание</value>
  </data>
  <data name="FeaturedTopics" xml:space="preserve">
    <value>Избранные темы</value>
  </data>
  <data name="TefsirTitle" xml:space="preserve">
    <value>Тафсир</value>
  </data>
  <data name="TefsirSubtitle" xml:space="preserve">
    <value>Толкование Корана</value>
  </data>
  <data name="TefsirDescription" xml:space="preserve">
    <value>Подробные объяснения и толкования коранических аятов.</value>
  </data>
  <data name="HadisTitle" xml:space="preserve">
    <value>Хадисы</value>
  </data>
  <data name="HadisSubtitle" xml:space="preserve">
    <value>Изречения Пророка</value>
  </data>
  <data name="HadisDescription" xml:space="preserve">
    <value>Изучение и объяснение хадисов нашего Пророка (мир ему).</value>
  </data>
  <data name="FikhTitle" xml:space="preserve">
    <value>Фикх</value>
  </data>
  <data name="FikhSubtitle" xml:space="preserve">
    <value>Исламское право</value>
  </data>
  <data name="FikhDescription" xml:space="preserve">
    <value>Фикхские решения по вопросам поклонения и взаимоотношений.</value>
  </data>
  <data name="SiyerTitle" xml:space="preserve">
    <value>Сира</value>
  </data>
  <data name="SiyerSubtitle" xml:space="preserve">
    <value>Жизнь нашего Пророка</value>
  </data>
  <data name="SiyerDescription" xml:space="preserve">
    <value>Жизнь и примерность Пророка Мухаммеда (мир ему).</value>
  </data>
  <data name="ViewAllLessons" xml:space="preserve">
    <value>Посмотреть все уроки</value>
  </data>
  
  <!-- Contact Page -->
  <data name="contactPageTitle" xml:space="preserve">
    <value>Контакты</value>
  </data>
  <data name="contactPageSubtitle" xml:space="preserve">
    <value>Свяжитесь с нами</value>
  </data>
  <data name="contactInfoTitle" xml:space="preserve">
    <value>Контактная информация</value>
  </data>
  <data name="emailLabel" xml:space="preserve">
    <value>Электронная почта</value>
  </data>
  <data name="websiteLabel" xml:space="preserve">
    <value>Веб-сайт</value>
  </data>
  <data name="workingHoursLabel" xml:space="preserve">
    <value>Рабочие часы</value>
  </data>
  <data name="workingHoursTime" xml:space="preserve">
    <value>Понедельник - Пятница: 09:00 - 18:00</value>
  </data>
  <data name="socialMediaTitle" xml:space="preserve">
    <value>Социальные сети</value>
  </data>
  <data name="sendMessageTitle" xml:space="preserve">
    <value>Отправить сообщение</value>
  </data>
  <data name="nameLabel" xml:space="preserve">
    <value>Полное имя *</value>
  </data>
  <data name="subjectLabel" xml:space="preserve">
    <value>Тема</value>
  </data>
  <data name="messageLabel" xml:space="preserve">
    <value>Ваше сообщение *</value>
  </data>
  <data name="sendButtonText" xml:space="preserve">
    <value>Отправить сообщение</value>
  </data>
  
  <!-- About Page -->
  <data name="aboutPageTitle" xml:space="preserve">
    <value>Шейх Ясин Караташ</value>
  </data>
  <data name="aboutPageSubtitle" xml:space="preserve">
    <value>Образование и преподавание исламских наук</value>
  </data>
  <data name="lifeEducationTitle" xml:space="preserve">
    <value>Жизнь и образование</value>
  </data>
  <data name="aboutParagraph1" xml:space="preserve">
    <value>Шейх Ясин Караташ имеет многолетний опыт в образовании и преподавании в области исламских наук. Он обладает глубокими знаниями в Коране, Хадисах, Тафсире и Фикхе.</value>
  </data>
  <data name="aboutParagraph2" xml:space="preserve">
    <value>Наш учитель, выпускник богословского факультета, изучил и применил традиционные исламские методы обучения в дополнение к академическому образованию. Он стремится передать своим студентам как теоретические, так и практические знания.</value>
  </data>
  <data name="aboutParagraph3" xml:space="preserve">
    <value>Он ведет пионерскую работу в донесении исламских наук до более широкой аудитории, используя современные технологии.</value>
  </data>
  <data name="expertiseTitle" xml:space="preserve">
    <value>Области экспертизы</value>
  </data>
  
  <!-- Privacy Page -->
  <data name="privacyPageTitle" xml:space="preserve">
    <value>Политика конфиденциальности</value>
  </data>
  <data name="privacyIntro" xml:space="preserve">
    <value>На этой странице объясняется политика конфиденциальности веб-сайта yasinkaratas.com.tr.</value>
  </data>
  <data name="dataCollectionTitle" xml:space="preserve">
    <value>Собираемая информация</value>
  </data>
  <data name="dataCollectionText" xml:space="preserve">
    <value>При посещении нашего веб-сайта может собираться некоторая информация для улучшения пользовательского опыта. Эта информация включает ваш IP-адрес, тип браузера и страницы, которые вы посещаете.</value>
  </data>
  <data name="dataUsageTitle" xml:space="preserve">
    <value>Использование информации</value>
  </data>
  <data name="dataUsageText" xml:space="preserve">
    <value>Собранная информация используется только для улучшения производительности сайта и предоставления вам лучшего обслуживания. Личная информация не передается третьим лицам.</value>
  </data>
  <data name="cookiesTitle" xml:space="preserve">
    <value>Файлы cookie</value>
  </data>
  <data name="cookiesText" xml:space="preserve">
    <value>Наш веб-сайт может использовать файлы cookie для улучшения пользовательского опыта. Вы можете изменить настройки файлов cookie в своем браузере.</value>
  </data>
  <data name="contactForPrivacy" xml:space="preserve">
    <value>Если у вас есть вопросы о нашей политике конфиденциальности, вы можете связаться с нами.</value>
  </data>
</root>
