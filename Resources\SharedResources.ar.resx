<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this f  <data name="NavContact" xml:space="preserve">
    <value>اتصل بنا</value>
  </data>
  <data name="NavPrivacy" xml:space="preserve">
    <value>الخصوصية</value>
  </data>
  <data name="NavLogin" xml:space="preserve">
    <value>تسجيل الدخول</value>
  </data>is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Navigation -->
  <data name="NavHome" xml:space="preserve">
    <value>الرئيسية</value>
  </data>
  <data name="NavAbout" xml:space="preserve">
    <value>عن الشيخ</value>
  </data>
  <data name="NavQuran" xml:space="preserve">
    <value>القرآن</value>
  </data>
  <data name="NavLessons" xml:space="preserve">
    <value>الدروس</value>
  </data>
  <data name="NavVideos" xml:space="preserve">
    <value>الفيديوهات</value>
  </data>
  <data name="NavArticles" xml:space="preserve">
    <value>المقالات</value>
  </data>
  <data name="NavBooks" xml:space="preserve">
    <value>الكتب</value>
  </data>
  <data name="NavFatawa" xml:space="preserve">
    <value>الفتاوى</value>
  </data>
  <data name="NavContact" xml:space="preserve">
    <value>التواصل</value>
  </data>
  <data name="NavLogin" xml:space="preserve">
    <value>تسجيل الدخول</value>
  </data>
  
  <!-- Footer -->
  <data name="FooterQuickLinks" xml:space="preserve">
    <value>روابط سريعة</value>
  </data>
  <data name="FooterContactInfo" xml:space="preserve">
    <value>معلومات التواصل</value>
  </data>
  <data name="FooterRightsReserved" xml:space="preserve">
    <value>جميع الحقوق محفوظة.</value>
  </data>
  
  <!-- Common -->
  <data name="Welcome" xml:space="preserve">
    <value>أهلاً وسهلاً</value>
  </data>
  <data name="IslamicEducation" xml:space="preserve">
    <value>أنشطة تعليمية وتدريسية في مجال العلوم الإسلامية.</value>
  </data>
  <data name="SiteDescription" xml:space="preserve">
    <value>العلوم الإسلامية ودروس القرآن والتعليم الديني مع الشيخ ياسين كاراتاش.</value>
  </data>
  
  <!-- Index Page -->
  <data name="FeaturedTopics" xml:space="preserve">
    <value>المواضيع المميزة</value>
  </data>
  <data name="TefsirTitle" xml:space="preserve">
    <value>التفسير</value>
  </data>
  <data name="TefsirSubtitle" xml:space="preserve">
    <value>تفسير القرآن الكريم</value>
  </data>
  <data name="TefsirDescription" xml:space="preserve">
    <value>دروس مفصلة حول معنى وتفسير القرآن الكريم.</value>
  </data>
  <data name="HadisTitle" xml:space="preserve">
    <value>الحديث</value>
  </data>
  <data name="HadisSubtitle" xml:space="preserve">
    <value>دروس الحديث الشريف</value>
  </data>
  <data name="HadisDescription" xml:space="preserve">
    <value>سنن وأحاديث النبي محمد صلى الله عليه وسلم.</value>
  </data>
  <data name="FikhTitle" xml:space="preserve">
    <value>الفقه</value>
  </data>
  <data name="FikhSubtitle" xml:space="preserve">
    <value>الفقه الإسلامي</value>
  </data>
  <data name="FikhDescription" xml:space="preserve">
    <value>الأحكام الفقهية في العبادات والمعاملات.</value>
  </data>
  <data name="SiyerTitle" xml:space="preserve">
    <value>السيرة</value>
  </data>
  <data name="SiyerSubtitle" xml:space="preserve">
    <value>حياة النبي</value>
  </data>
  <data name="SiyerDescription" xml:space="preserve">
    <value>حياة ومثال النبي محمد صلى الله عليه وسلم.</value>
  </data>
  <data name="ViewAllLessons" xml:space="preserve">
    <value>عرض جميع الدروس</value>
  </data>
  <!-- Contact Page -->
  <data name="contactPageTitle" xml:space="preserve">
    <value>اتصل بنا</value>
  </data>
  <data name="contactPageSubtitle" xml:space="preserve">
    <value>تواصل معنا</value>
  </data>
  <data name="contactInfoTitle" xml:space="preserve">
    <value>معلومات الاتصال</value>
  </data>
  <data name="emailLabel" xml:space="preserve">
    <value>البريد الإلكتروني</value>
  </data>
  <data name="websiteLabel" xml:space="preserve">
    <value>الموقع الإلكتروني</value>
  </data>
  <data name="workingHoursLabel" xml:space="preserve">
    <value>ساعات العمل</value>
  </data>
  <data name="workingHoursTime" xml:space="preserve">
    <value>الاثنين - الجمعة: 09:00 - 18:00</value>
  </data>
  <data name="socialMediaTitle" xml:space="preserve">
    <value>وسائل التواصل الاجتماعي</value>
  </data>
  <data name="sendMessageTitle" xml:space="preserve">
    <value>إرسال رسالة</value>
  </data>
  <data name="nameLabel" xml:space="preserve">
    <value>الاسم الكامل *</value>
  </data>
  <data name="subjectLabel" xml:space="preserve">
    <value>الموضوع</value>
  </data>
  <data name="messageLabel" xml:space="preserve">
    <value>رسالتك *</value>
  </data>
  <data name="sendButtonText" xml:space="preserve">
    <value>إرسال الرسالة</value>
  </data>
  <!-- About Page -->
  <data name="aboutPageTitle" xml:space="preserve">
    <value>الشيخ ياسين كاراتاش</value>
  </data>
  <data name="aboutPageSubtitle" xml:space="preserve">
    <value>التعليم والتدريس في العلوم الإسلامية</value>
  </data>
  <data name="lifeEducationTitle" xml:space="preserve">
    <value>الحياة والتعليم</value>
  </data>
  <data name="aboutParagraph1" xml:space="preserve">
    <value>الشيخ ياسين كاراتاش لديه سنوات من الخبرة في التعليم والتدريس في مجال العلوم الإسلامية. لديه معرفة عميقة في القرآن والحديث والتفسير والفقه.</value>
  </data>
  <data name="aboutParagraph2" xml:space="preserve">
    <value>أستاذنا، خريج كلية الإلهيات، تعلم وطبق أساليب التعليم الإسلامي التقليدية بالإضافة إلى التعليم الأكاديمي. يهدف إلى نقل المعرفة النظرية والعملية لطلابه.</value>
  </data>
  <data name="aboutParagraph3" xml:space="preserve">
    <value>يقوم بأعمال رائدة في إيصال العلوم الإسلامية إلى جماهير أوسع باستخدام تكنولوجيا اليوم.</value>
  </data>
  <data name="expertiseTitle" xml:space="preserve">
    <value>مجالات الخبرة</value>
  </data>
  <!-- Privacy Page -->
  <data name="privacyPageTitle" xml:space="preserve">
    <value>سياسة الخصوصية</value>
  </data>
  <data name="privacyIntro" xml:space="preserve">
    <value>توضح هذه الصفحة سياسة الخصوصية لموقع yasinkaratas.com.tr.</value>
  </data>
  <data name="dataCollectionTitle" xml:space="preserve">
    <value>المعلومات المجمعة</value>
  </data>
  <data name="dataCollectionText" xml:space="preserve">
    <value>عند زيارة موقعنا، قد يتم جمع بعض المعلومات لتحسين تجربة المستخدم. تشمل هذه المعلومات عنوان IP الخاص بك ونوع المتصفح والصفحات التي تزورها.</value>
  </data>
  <data name="dataUsageTitle" xml:space="preserve">
    <value>استخدام المعلومات</value>
  </data>
  <data name="dataUsageText" xml:space="preserve">
    <value>تُستخدم المعلومات المجمعة فقط لتحسين أداء الموقع وتقديم خدمة أفضل لك. لا تتم مشاركة المعلومات الشخصية مع أطراف ثالثة.</value>
  </data>
  <data name="cookiesTitle" xml:space="preserve">
    <value>ملفات تعريف الارتباط</value>
  </data>
  <data name="cookiesText" xml:space="preserve">
    <value>قد يستخدم موقعنا ملفات تعريف الارتباط لتحسين تجربة المستخدم. يمكنك تغيير إعدادات ملفات تعريف الارتباط من متصفحك.</value>
  </data>
  <data name="contactForPrivacy" xml:space="preserve">
    <value>إذا كان لديك أسئلة حول سياسة الخصوصية الخاصة بنا، يمكنك الاتصال بنا.</value>
  </data>
</root>
