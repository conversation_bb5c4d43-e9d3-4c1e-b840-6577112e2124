<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Navigation -->
  <data name="NavHome" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="NavAbout" xml:space="preserve">
    <value>About</value>
  </data>
  <data name="NavQuran" xml:space="preserve">
    <value>Quran</value>
  </data>
  <data name="NavLessons" xml:space="preserve">
    <value>Lessons</value>
  </data>
  <data name="NavVideos" xml:space="preserve">
    <value>Videos</value>
  </data>
  <data name="NavArticles" xml:space="preserve">
    <value>Articles</value>
  </data>
  <data name="NavBooks" xml:space="preserve">
    <value>Books</value>
  </data>
  <data name="NavFatawa" xml:space="preserve">
    <value>Fatawa</value>
  </data>
  <data name="NavContact" xml:space="preserve">
    <value>Contact</value>
  </data>
  <data name="NavPrivacy" xml:space="preserve">
    <value>Privacy</value>
  </data>
  <data name="NavLogin" xml:space="preserve">
    <value>Login</value>
  </data>
  
  <!-- Footer -->
  <data name="FooterQuickLinks" xml:space="preserve">
    <value>Quick Links</value>
  </data>
  <data name="FooterContactInfo" xml:space="preserve">
    <value>Contact</value>
  </data>
  <data name="FooterRightsReserved" xml:space="preserve">
    <value>All rights reserved.</value>
  </data>
  
  <!-- Common -->
  <data name="Welcome" xml:space="preserve">
    <value>Welcome</value>
  </data>
  <data name="IslamicEducation" xml:space="preserve">
    <value>Educational and teaching activities in the field of Islamic studies.</value>
  </data>
  <data name="SiteDescription" xml:space="preserve">
    <value>Islamic studies, Quran lessons and religious education with Yasin Karataş Hoca.</value>
  </data>
  
  <!-- Index Page -->
  <data name="FeaturedTopics" xml:space="preserve">
    <value>Featured Topics</value>
  </data>
  <data name="TefsirTitle" xml:space="preserve">
    <value>Tafsir</value>
  </data>
  <data name="TefsirSubtitle" xml:space="preserve">
    <value>Quran Commentary</value>
  </data>
  <data name="TefsirDescription" xml:space="preserve">
    <value>Detailed lessons on the meaning and interpretation of the Holy Quran.</value>
  </data>
  <data name="HadisTitle" xml:space="preserve">
    <value>Hadith</value>
  </data>
  <data name="HadisSubtitle" xml:space="preserve">
    <value>Hadith Lessons</value>
  </data>
  <data name="HadisDescription" xml:space="preserve">
    <value>The traditions and sayings of the Prophet Muhammad.</value>
  </data>
  <data name="FikhTitle" xml:space="preserve">
    <value>Fiqh</value>
  </data>
  <data name="FikhSubtitle" xml:space="preserve">
    <value>Islamic Law</value>
  </data>
  <data name="FikhDescription" xml:space="preserve">
    <value>Jurisprudential rulings on worship and transactions.</value>
  </data>
  <data name="SiyerTitle" xml:space="preserve">
    <value>Sirah</value>
  </data>
  <data name="SiyerSubtitle" xml:space="preserve">
    <value>Life of the Prophet</value>
  </data>
  <data name="SiyerDescription" xml:space="preserve">
    <value>The life and example of Prophet Muhammad (SAW).</value>
  </data>
  <data name="ViewAllLessons" xml:space="preserve">
    <value>View All Lessons</value>
  </data>
  <!-- Contact Page -->
  <data name="contactPageTitle" xml:space="preserve">
    <value>Contact</value>
  </data>
  <data name="contactPageSubtitle" xml:space="preserve">
    <value>Get in touch with us</value>
  </data>
  <data name="contactInfoTitle" xml:space="preserve">
    <value>Contact Information</value>
  </data>
  <data name="emailLabel" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="websiteLabel" xml:space="preserve">
    <value>Website</value>
  </data>
  <data name="workingHoursLabel" xml:space="preserve">
    <value>Working Hours</value>
  </data>
  <data name="workingHoursTime" xml:space="preserve">
    <value>Monday - Friday: 09:00 - 18:00</value>
  </data>
  <data name="socialMediaTitle" xml:space="preserve">
    <value>Social Media</value>
  </data>
  <data name="sendMessageTitle" xml:space="preserve">
    <value>Send Message</value>
  </data>
  <data name="nameLabel" xml:space="preserve">
    <value>Full Name *</value>
  </data>
  <data name="subjectLabel" xml:space="preserve">
    <value>Subject</value>
  </data>
  <data name="messageLabel" xml:space="preserve">
    <value>Your Message *</value>
  </data>
  <data name="sendButtonText" xml:space="preserve">
    <value>Send Message</value>
  </data>
  <!-- About Page -->
  <data name="aboutPageTitle" xml:space="preserve">
    <value>Sheikh Yasin Karataş</value>
  </data>
  <data name="aboutPageSubtitle" xml:space="preserve">
    <value>Education and Teaching in Islamic Sciences</value>
  </data>
  <data name="lifeEducationTitle" xml:space="preserve">
    <value>Life and Education</value>
  </data>
  <data name="aboutParagraph1" xml:space="preserve">
    <value>Sheikh Yasin Karataş has years of experience in education and teaching in the field of Islamic sciences. He has deep knowledge in Quran, Hadith, Tafsir and Fiqh.</value>
  </data>
  <data name="aboutParagraph2" xml:space="preserve">
    <value>Our teacher, a graduate of the Faculty of Theology, has learned and applied traditional Islamic education methods as well as academic education. He aims to transfer both theoretical and practical knowledge to his students.</value>
  </data>
  <data name="aboutParagraph3" xml:space="preserve">
    <value>He is doing pioneering work in reaching Islamic sciences to wider audiences using today's technology.</value>
  </data>
  <data name="expertiseTitle" xml:space="preserve">
    <value>Areas of Expertise</value>
  </data>
  <!-- Privacy Page -->
  <data name="privacyPageTitle" xml:space="preserve">
    <value>Privacy Policy</value>
  </data>
  <data name="privacyIntro" xml:space="preserve">
    <value>This page explains the privacy policy of yasinkaratas.com.tr website.</value>
  </data>
  <data name="dataCollectionTitle" xml:space="preserve">
    <value>Information Collected</value>
  </data>
  <data name="dataCollectionText" xml:space="preserve">
    <value>When you visit our website, some information may be collected to improve user experience. This information includes your IP address, browser type, and pages you visit.</value>
  </data>
  <data name="dataUsageTitle" xml:space="preserve">
    <value>Use of Information</value>
  </data>
  <data name="dataUsageText" xml:space="preserve">
    <value>The collected information is used only to improve site performance and provide you with better service. Personal information is not shared with third parties.</value>
  </data>
  <data name="cookiesTitle" xml:space="preserve">
    <value>Cookies</value>
  </data>
  <data name="cookiesText" xml:space="preserve">
    <value>Our website may use cookies to improve user experience. You can change cookie settings from your browser.</value>
  </data>
  <data name="contactForPrivacy" xml:space="preserve">
    <value>If you have questions about our privacy policy, you can contact us.</value>
  </data>
</root>
