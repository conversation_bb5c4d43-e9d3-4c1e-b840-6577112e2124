<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Navigation -->
  <data name="NavHome" xml:space="preserve">
    <value>Startseite</value>
  </data>
  <data name="NavAbout" xml:space="preserve">
    <value>Über uns</value>
  </data>
  <data name="NavQuran" xml:space="preserve">
    <value>Koran</value>
  </data>
  <data name="NavLessons" xml:space="preserve">
    <value>Lektionen</value>
  </data>
  <data name="NavVideos" xml:space="preserve">
    <value>Videos</value>
  </data>
  <data name="NavArticles" xml:space="preserve">
    <value>Artikel</value>
  </data>
  <data name="NavBooks" xml:space="preserve">
    <value>Bücher</value>
  </data>
  <data name="NavFatawa" xml:space="preserve">
    <value>Fatawa</value>
  </data>
  <data name="NavContact" xml:space="preserve">
    <value>Kontakt</value>
  </data>
  <data name="NavPrivacy" xml:space="preserve">
    <value>Datenschutz</value>
  </data>
  <data name="NavLogin" xml:space="preserve">
    <value>Anmelden</value>
  </data>
  
  <!-- Footer -->
  <data name="FooterQuickLinks" xml:space="preserve">
    <value>Schnelle Links</value>
  </data>
  <data name="FooterContactInfo" xml:space="preserve">
    <value>Kontakt</value>
  </data>
  <data name="FooterRightsReserved" xml:space="preserve">
    <value>Alle Rechte vorbehalten.</value>
  </data>
  
  <!-- Common -->
  <data name="Welcome" xml:space="preserve">
    <value>Willkommen</value>
  </data>
  <data name="IslamicEducation" xml:space="preserve">
    <value>Bildungs- und Lehraktivitäten im Bereich der Islamwissenschaften.</value>
  </data>
  <data name="SiteDescription" xml:space="preserve">
    <value>Islamwissenschaften, Koranstunden und Religionsunterricht mit Yasin Karataş Hoca.</value>
  </data>
  
  <!-- Index Page -->
  <data name="FeaturedTopics" xml:space="preserve">
    <value>Ausgewählte Themen</value>
  </data>
  <data name="TefsirTitle" xml:space="preserve">
    <value>Tafsir</value>
  </data>
  <data name="TefsirSubtitle" xml:space="preserve">
    <value>Koran-Kommentar</value>
  </data>
  <data name="TefsirDescription" xml:space="preserve">
    <value>Detaillierte Lektionen über die Bedeutung und Interpretation des Heiligen Korans.</value>
  </data>
  <data name="HadisTitle" xml:space="preserve">
    <value>Hadith</value>
  </data>
  <data name="HadisSubtitle" xml:space="preserve">
    <value>Hadith-Lektionen</value>
  </data>
  <data name="HadisDescription" xml:space="preserve">
    <value>Die Traditionen und Aussagen des Propheten Muhammad.</value>
  </data>
  <data name="FikhTitle" xml:space="preserve">
    <value>Fiqh</value>
  </data>
  <data name="FikhSubtitle" xml:space="preserve">
    <value>Islamisches Recht</value>
  </data>
  <data name="FikhDescription" xml:space="preserve">
    <value>Juristische Bestimmungen zu Gottesdienst und Transaktionen.</value>
  </data>
  <data name="SiyerTitle" xml:space="preserve">
    <value>Sirah</value>
  </data>
  <data name="SiyerSubtitle" xml:space="preserve">
    <value>Leben des Propheten</value>
  </data>
  <data name="SiyerDescription" xml:space="preserve">
    <value>Das Leben und Beispiel des Propheten Muhammad (Friede sei mit ihm).</value>
  </data>
  <data name="ViewAllLessons" xml:space="preserve">
    <value>Alle Lektionen anzeigen</value>
  </data>
  <!-- Contact Page -->
  <data name="contactPageTitle" xml:space="preserve">
    <value>Kontakt</value>
  </data>
  <data name="contactPageSubtitle" xml:space="preserve">
    <value>Kontaktieren Sie uns</value>
  </data>
  <data name="contactInfoTitle" xml:space="preserve">
    <value>Kontaktinformationen</value>
  </data>
  <data name="emailLabel" xml:space="preserve">
    <value>E-Mail</value>
  </data>
  <data name="websiteLabel" xml:space="preserve">
    <value>Website</value>
  </data>
  <data name="workingHoursLabel" xml:space="preserve">
    <value>Arbeitszeiten</value>
  </data>
  <data name="workingHoursTime" xml:space="preserve">
    <value>Montag - Freitag: 09:00 - 18:00</value>
  </data>
  <data name="socialMediaTitle" xml:space="preserve">
    <value>Soziale Medien</value>
  </data>
  <data name="sendMessageTitle" xml:space="preserve">
    <value>Nachricht senden</value>
  </data>
  <data name="nameLabel" xml:space="preserve">
    <value>Vollständiger Name *</value>
  </data>
  <data name="subjectLabel" xml:space="preserve">
    <value>Betreff</value>
  </data>
  <data name="messageLabel" xml:space="preserve">
    <value>Ihre Nachricht *</value>
  </data>
  <data name="sendButtonText" xml:space="preserve">
    <value>Nachricht senden</value>
  </data>
  <!-- About Page -->
  <data name="aboutPageTitle" xml:space="preserve">
    <value>Scheich Yasin Karataş</value>
  </data>
  <data name="aboutPageSubtitle" xml:space="preserve">
    <value>Bildung und Lehre in Islamwissenschaften</value>
  </data>
  <data name="lifeEducationTitle" xml:space="preserve">
    <value>Leben und Bildung</value>
  </data>
  <data name="aboutParagraph1" xml:space="preserve">
    <value>Scheich Yasin Karataş hat jahrelange Erfahrung in Bildung und Lehre im Bereich der Islamwissenschaften. Er hat tiefes Wissen in Koran, Hadith, Tafsir und Fiqh.</value>
  </data>
  <data name="aboutParagraph2" xml:space="preserve">
    <value>Unser Lehrer, ein Absolvent der Theologischen Fakultät, hat neben der akademischen Bildung auch traditionelle islamische Bildungsmethoden gelernt und angewendet. Er zielt darauf ab, sowohl theorisches als auch praktisches Wissen an seine Schüler zu vermitteln.</value>
  </data>
  <data name="aboutParagraph3" xml:space="preserve">
    <value>Er leistet Pionierarbeit dabei, islamische Wissenschaften mit heutiger Technologie einem breiteren Publikum zugänglich zu machen.</value>
  </data>
  <data name="expertiseTitle" xml:space="preserve">
    <value>Fachbereiche</value>
  </data>
  <!-- Privacy Page -->
  <data name="privacyPageTitle" xml:space="preserve">
    <value>Datenschutzerklärung</value>
  </data>
  <data name="privacyIntro" xml:space="preserve">
    <value>Diese Seite erklärt die Datenschutzerklärung der Website yasinkaratas.com.tr.</value>
  </data>
  <data name="dataCollectionTitle" xml:space="preserve">
    <value>Gesammelte Informationen</value>
  </data>
  <data name="dataCollectionText" xml:space="preserve">
    <value>Wenn Sie unsere Website besuchen, können einige Informationen gesammelt werden, um die Benutzererfahrung zu verbessern. Diese Informationen umfassen Ihre IP-Adresse, Ihren Browser-Typ und die von Ihnen besuchten Seiten.</value>
  </data>
  <data name="dataUsageTitle" xml:space="preserve">
    <value>Verwendung der Informationen</value>
  </data>
  <data name="dataUsageText" xml:space="preserve">
    <value>Die gesammelten Informationen werden nur verwendet, um die Website-Leistung zu verbessern und Ihnen besseren Service zu bieten. Persönliche Informationen werden nicht mit Dritten geteilt.</value>
  </data>
  <data name="cookiesTitle" xml:space="preserve">
    <value>Cookies</value>
  </data>
  <data name="cookiesText" xml:space="preserve">
    <value>Unsere Website kann Cookies verwenden, um die Benutzererfahrung zu verbessern. Sie können Cookie-Einstellungen in Ihrem Browser ändern.</value>
  </data>
  <data name="contactForPrivacy" xml:space="preserve">
    <value>Wenn Sie Fragen zu unserer Datenschutzerklärung haben, können Sie uns kontaktieren.</value>
  </data>
</root>
