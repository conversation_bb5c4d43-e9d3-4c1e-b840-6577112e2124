/* _content/yasinkaratas.com.tr/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-rqmqet7tzw] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-rqmqet7tzw] {
  color: #0077cc;
}

.btn-primary[b-rqmqet7tzw] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-rqmqet7tzw], .nav-pills .show > .nav-link[b-rqmqet7tzw] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-rqmqet7tzw] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-rqmqet7tzw] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-rqmqet7tzw] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-rqmqet7tzw] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-rqmqet7tzw] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
